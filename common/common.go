package common

import (
	"fmt"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/consts"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/log"
	"go.uber.org/zap"
	"math/big"
	"os"
	"runtime/debug"
)

func Recover() {
	if r := recover(); r != nil {
		log.Logger.Error("Recovered", zap.String("panic", fmt.Sprintf("%v", r)), zap.String("stack", string(debug.Stack())))
	}
}

func SqrtPriceToPrice(sqrtPriceX64 *big.Int, decimals0, decimals1 int) *big.Float {
	// 1. 计算 2^64
	twoPow64 := new(big.Int).Exp(big.NewInt(2), big.NewInt(64), nil)

	// 2. 将 sqrtPriceX64 转换为 big.Float
	sqrtPriceFloat := new(big.Float).SetInt(sqrtPriceX64)
	sqrtPriceFloat = new(big.Float).Quo(sqrtPriceFloat, new(big.Float).SetInt(twoPow64))

	// 3. 平方得到价格比例
	priceRatio := new(big.Float).Mul(sqrtPriceFloat, sqrtPriceFloat)

	// 4. 处理代币小数位数
	decimalsFactor := new(big.Float).SetInt(
		new(big.Int).Exp(big.NewInt(10), big.NewInt(int64(decimals0-decimals1)), nil),
	)

	// 5. 最终价格计算
	finalPrice := new(big.Float).Mul(priceRatio, decimalsFactor)

	return finalPrice
}

// GetJuChainBlockTime juchain 一秒一个块
func GetJuChainBlockTime(height int64) int64 {
	return 1742914214 + height
}

func IsEnvLocal() bool {
	return os.Getenv("ENV") == consts.EnvLocal
}
