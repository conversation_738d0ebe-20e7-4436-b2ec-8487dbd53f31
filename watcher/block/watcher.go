package block

import (
	"context"
	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/common"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/config"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/consts"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/log"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/watcher/uniswap"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/wrapper"
	"go.uber.org/zap"
	"math/big"
	"sync/atomic"
	"time"
)

type Watcher struct {
	ctx context.Context

	parseContexts []*parseContext
}

func NewWatcher(cfgs []config.ChainConfig) *Watcher {
	ctx := context.Background()

	var parseContexts []*parseContext
	for _, cfg := range cfgs {
		if !cfg.ForwardEnabled && !cfg.BackwardEnabled {
			continue
		}

		cli, err := wrapper.Dial(cfg.RpcUrl)
		if err != nil {
			panic(err)
		}

		chainID, err := cli.ChainID(ctx)
		if err != nil {
			panic(err)
		}

		if new(big.Int).SetUint64(cfg.ChainId).Cmp(chainID) != 0 {
			log.Logger.Error("chainId not match", zap.Uint64("chainId", cfg.ChainId), zap.Uint64("rpcChainId", chainID.Uint64()))
			panic("chainId not match")
		}

		parser := uniswap.NewParser(cfg)

		pCtx := parseContext{
			ctx:             ctx,
			cli:             cli,
			chainId:         chainID,
			forwardEnabled:  cfg.ForwardEnabled,
			backwardEnabled: cfg.BackwardEnabled,
			uniswapV2Parser: &parser,
			maxBlockStep:    uint64(cfg.MaxBlockStep),
		}

		parseContexts = append(parseContexts, &pCtx)
	}

	return &Watcher{
		ctx:           ctx,
		parseContexts: parseContexts,
	}
}

func (w *Watcher) Start() {

	for _, pCtx := range w.parseContexts {
		pCtx.start()
	}

}

type parseContext struct {
	ctx     context.Context
	chainId *big.Int
	cli     *wrapper.Client

	forwardEnabled  bool
	backwardEnabled bool

	latestHeight          atomic.Uint64
	currentForwardHeight  atomic.Uint64
	currentBackwardHeight atomic.Uint64

	maxBlockStep uint64

	uniswapV2Parser *uniswap.Parser
}

func (w *parseContext) start() {
	log.Logger.Info("load block watcher", zap.Any("chainId", w.chainId))
	w.updateLatestHeight()

	height, err := w.cli.BlockNumber(w.ctx)
	if err != nil {
		panic(err)
	}

	w.currentForwardHeight.Store(height)

	go w.watchForward()
}

func (w *parseContext) updateLatestHeight() {
	go func() {
		for {
			height, err := w.cli.BlockNumber(w.ctx)
			if err != nil {
				log.Logger.Error("get block number error", zap.Error(err))
				time.Sleep(time.Millisecond * 500)
				continue
			}

			w.latestHeight.Store(height)
			time.Sleep(time.Millisecond * 500)
		}
	}()
}

func (w *parseContext) watchForward() {
	if !w.forwardEnabled {
		return
	}

	for {
		latestHeight := w.latestHeight.Load()
		if latestHeight == 0 {
			time.Sleep(time.Millisecond * 500)
			continue
		}

		height := w.currentForwardHeight.Load()
		if height >= latestHeight {
			time.Sleep(time.Millisecond * 500)
			continue
		}

		toHeight := height + w.maxBlockStep
		if toHeight > latestHeight {
			toHeight = latestHeight
		}

		w.parseByBlock(height, toHeight)

		w.currentForwardHeight.Store(toHeight + 1)
		log.Logger.Info("parsed forward", zap.Uint64("chainId", w.chainId.Uint64()), zap.Uint64("height", toHeight))
		time.Sleep(time.Millisecond * 500)
	}
}

func (w *parseContext) parseByBlock(from, to uint64) {

	wlogs, err := w.cli.FilterLogs(w.ctx, ethereum.FilterQuery{
		FromBlock: big.NewInt(int64(from)),
		ToBlock:   big.NewInt(int64(to)),
		Topics: [][]common.Hash{
			{
				common.HexToHash(consts.UniswapV2SwapTopic),
				common.HexToHash(consts.UniswapV2SyncTopic),
			},
		},
	})

	if err != nil {
		log.Logger.Error("filter wlogs error", zap.Error(err))
		time.Sleep(time.Millisecond * 500)
		return
	}

	for _, wlog := range wlogs {
		switch wlog.Topics[0].String() {
		case consts.UniswapV2SwapTopic:
			go w.uniswapV2Parser.ParseSwap(wlog) //异步
		case consts.UniswapV2SyncTopic:
			w.uniswapV2Parser.ParseSync(wlog) //同步
		}
	}
}

func (w *parseContext) WatchBackward() { //todo
	if !w.backwardEnabled {
		return
	}
}
