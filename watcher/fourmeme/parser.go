package fourmeme

import (
	"context"
	"encoding/json"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/wrapper"
	"math/big"
	"strings"

	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/forta-network/go-multicall"
	"github.com/shopspring/decimal"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/config"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/consts"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/contracts/erc20"
	fmeme "gitlab.jcwork.net/cedefi/cedefi-evm-indexer/contracts/fourmeme/tokenmanagerv2"
	muticalltype "gitlab.jcwork.net/cedefi/cedefi-evm-indexer/contracts/muticall"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/db"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/db/models"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/log"
	types2 "gitlab.jcwork.net/cedefi/cedefi-evm-indexer/types"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/watcher/native_price"
	"go.uber.org/zap"
)

type Parser struct {
	rpcUrl          string
	cli             *ethclient.Client
	ws              *ethclient.Client
	chainId         *big.Int
	wNative         string
	wNativeDecimals int
	enabled         bool
}

func NewParser(chain config.ChainConfig) Parser {
	if !chain.FourMemeEnabled {
		return Parser{enabled: false}
	}

	cli, err := ethclient.Dial(chain.RpcUrl)
	if err != nil {
		panic(err)
	}

	chainID, err := cli.ChainID(context.Background())
	if err != nil {
		panic(err)
	}

	if new(big.Int).SetUint64(chain.ChainId).Cmp(chainID) != 0 {
		log.Logger.Error("chainId not match", zap.Uint64("chainId", chain.ChainId), zap.Uint64("rpcChainId", chainID.Uint64()))
		panic("chainId not match")
	}

	var ws *ethclient.Client
	for ws == nil {
		ws, err = ethclient.Dial(chain.WsUrl)
		if err != nil {
			log.Logger.Error("dial ws error", zap.Error(err), zap.String("url", chain.WsUrl))
		}
	}

	return Parser{
		rpcUrl:          chain.RpcUrl,
		cli:             cli,
		ws:              ws,
		chainId:         chainID,
		wNative:         chain.WNative,
		wNativeDecimals: chain.WNativeDecimals,
		enabled:         chain.FourMemeEnabled,
	}
}

func (w Parser) ParseSwap(eventLog wrapper.Log) {

}

func (w Parser) parseBuy(purchaseLog *fmeme.TokenManagerV2TokenPurchase) {

	tokenModel, err := w.getAndSaveTokenOnChain(purchaseLog.Token.String())
	if err != nil {
		log.Logger.Error("get token error", zap.Error(err))
		return
	}

	pairAddr := purchaseLog.Token
	makerAddr := purchaseLog.Account

	baseAmount := new(big.Int).Sub(purchaseLog.Amount, purchaseLog.Fee)
	quoteAmount := purchaseLog.Cost

	nativePrice := decimal.NewFromFloat(native_price.GetPrice(w.chainId.Uint64()))
	volumeInUsd := decimal.NewFromBigInt(quoteAmount, int32(0-w.wNativeDecimals)).Mul(nativePrice)
	priceInNative := decimal.NewFromBigInt(purchaseLog.Price, int32(0-18))
	priceInUsd := priceInNative.Mul(nativePrice)

	totalSupply := decimal.NewFromBigInt(consts.FourMemeTokenSupply, int32(0-tokenModel.Decimals))
	mcInNative := priceInUsd.Mul(totalSupply)
	log.Logger.Info("mc", zap.String("log", mcInNative.String()))

	dexName := consts.ChainDex[w.chainId.Uint64()][strings.ToLower("")] //TODO 需要考虑该字段应为池子的 dex 还是 router 的 dex
	if dexName == "" {
		dexName = consts.DexNameUnknown
	}

	activity := models.Activity{
		TokenAddress:     purchaseLog.Token.String(),
		PairAddress:      pairAddr.String(),
		BaseAddress:      purchaseLog.Token.String(),
		BaseDecimals:     consts.FourMemeTokenDecimals,
		QuoteAddress:     w.wNative,
		QuoteDecimals:    w.wNativeDecimals,
		Act:              types2.ActivityType_Buy,
		Maker:            makerAddr.String(),
		BaseAmount:       decimal.NewFromBigInt(baseAmount, 0),
		QuoteAmount:      decimal.NewFromBigInt(quoteAmount, 0),
		PriceInNative:    priceInNative,
		PriceInUsd:       priceInUsd,
		NativePriceInUsd: nativePrice,
		VolumeInUsd:      volumeInUsd,
		Height:           int64(purchaseLog.Raw.BlockNumber),
		Tx:               purchaseLog.Raw.TxHash.String(),
		Idx:              int64(purchaseLog.Raw.Index),
		//Ts:               int64(ts), TODO 需要找到合适的交易时间
		DexName: string(dexName),
		ChainId: w.chainId.Uint64(),
	}
	db.GetSwapStoreChannel(w.chainId.Uint64()) <- &activity //batch insert activity

	bytes, _ := json.Marshal(activity)
	log.Logger.Info("parse swap log", zap.Uint64("chainId", w.chainId.Uint64()), zap.String("log", string(bytes)))
	// TODO 当前为混合池子，所有的 token 在同一个池子里，无法以 pairAddress，存储该信息，可以考虑以 tokenAddress 替代 pairAddress
	// TODO 需要找到查询池子中币对数量的方法
	/*	db.UpdatePoolStatsCache(w.chainId, activity.PairAddress, consts.PoolExpireTime,
		consts.PoolStatMcInNativeRedisKey, mcInNative.String(),
		consts.PoolStatPriceInNativeRedisKey, priceInNative.String(),
		consts.PoolStatQuoteAmountRedisKey, reserves.Reserve0.String(),
		consts.PoolStatBaseAmountRedisKey, reserves.Reserve1.String(),
	)*/
}

func (w Parser) parseSell(saleLog *fmeme.TokenManagerV2TokenSale) {

	tokenModel, err := w.getAndSaveTokenOnChain(saleLog.Token.String())
	if err != nil {
		log.Logger.Error("get token error", zap.Error(err))
		return
	}

	pairAddr := saleLog.Raw.Address
	makerAddr := saleLog.Account

	baseAmount := new(big.Int).Sub(saleLog.Amount, saleLog.Fee)
	quoteAmount := saleLog.Cost

	nativePrice := decimal.NewFromFloat(native_price.GetPrice(w.chainId.Uint64()))
	volumeInUsd := decimal.NewFromBigInt(quoteAmount, int32(0-w.wNativeDecimals)).Mul(nativePrice)
	priceInNative := decimal.NewFromBigInt(saleLog.Price, int32(0-18))
	priceInUsd := priceInNative.Mul(nativePrice)

	totalSupply := decimal.NewFromBigInt(consts.FourMemeTokenSupply, int32(0-tokenModel.Decimals))
	mcInNative := priceInUsd.Mul(totalSupply)
	log.Logger.Info("mc", zap.String("log", mcInNative.String()))

	dexName := consts.ChainDex[w.chainId.Uint64()][strings.ToLower("")] //TODO 需要考虑该字段应为池子的 dex 还是 router 的 dex
	if dexName == "" {
		dexName = consts.DexNameUnknown
	}

	activity := models.Activity{
		TokenAddress:     saleLog.Token.String(),
		PairAddress:      pairAddr.String(),
		BaseAddress:      saleLog.Token.String(),
		BaseDecimals:     consts.FourMemeTokenDecimals,
		QuoteAddress:     w.wNative,
		QuoteDecimals:    w.wNativeDecimals,
		Act:              types2.ActivityType_Sell,
		Maker:            makerAddr.String(),
		BaseAmount:       decimal.NewFromBigInt(baseAmount, 0),
		QuoteAmount:      decimal.NewFromBigInt(quoteAmount, 0),
		PriceInNative:    priceInNative,
		PriceInUsd:       priceInUsd,
		NativePriceInUsd: nativePrice,
		VolumeInUsd:      volumeInUsd,
		Height:           int64(saleLog.Raw.BlockNumber),
		Tx:               saleLog.Raw.TxHash.String(),
		Idx:              int64(saleLog.Raw.Index),
		//Ts:               int64(ts), TODO 需要找到合适的交易时间
		DexName: string(dexName),
		ChainId: w.chainId.Uint64(),
	}
	db.GetSwapStoreChannel(w.chainId.Uint64()) <- &activity //batch insert activity

	bytes, _ := json.Marshal(activity)
	log.Logger.Info("parse swap log", zap.Uint64("chainId", w.chainId.Uint64()), zap.String("log", string(bytes)))
	// TODO 当前为混合池子，所有的 token 在同一个池子里，无法以 pairAddress，存储该信息，可以考虑以 tokenAddress 替代 pairAddress
	// TODO 需要找到查询池子中币对数量的方法
	/*	db.UpdatePoolStatsCache(w.chainId, activity.PairAddress, consts.PoolExpireTime,
		consts.PoolStatMcInNativeRedisKey, mcInNative.String(),
		consts.PoolStatPriceInNativeRedisKey, priceInNative.String(),
		consts.PoolStatQuoteAmountRedisKey, reserves.Reserve0.String(),
		consts.PoolStatBaseAmountRedisKey, reserves.Reserve1.String(),
	)*/
}

func (w Parser) getAndSaveTokenOnChain(tokenAddress string) (*models.Token, error) {
	tokenModel, _ := db.GetTokenWithCache(w.chainId, tokenAddress)
	if tokenModel != nil {
		return tokenModel, nil
	}

	caller, err := multicall.Dial(context.Background(), w.rpcUrl)
	if err != nil {
		log.Logger.Error("dial rpc error", zap.Error(err))
		return nil, err
	}

	tokenContract, err := multicall.NewContract(erc20.Erc20MetaData.ABI, tokenAddress)
	if err != nil {
		log.Logger.Error("new erc20 contract error", zap.Error(err))
		return nil, err
	}

	calls, err := caller.Call(nil,
		tokenContract.NewCall(new(muticalltype.StringResult), "name"),
		tokenContract.NewCall(new(muticalltype.StringResult), "symbol"),
		tokenContract.NewCall(new(muticalltype.Uint8Output), "decimals"),
		tokenContract.NewCall(new(muticalltype.BigIntOutput), "totalSupply"),
	)

	name := calls[0].Outputs.(*muticalltype.StringResult).Value
	symbol := calls[1].Outputs.(*muticalltype.StringResult).Value
	decimals := calls[2].Outputs.(*muticalltype.Uint8Output).Value
	totalSupply := calls[3].Outputs.(*muticalltype.BigIntOutput).Value

	tokenModel = &models.Token{
		Address:     tokenAddress,
		Name:        name,
		Symbol:      symbol,
		Image:       "", // TODO 如何链上查询 token 图片
		Decimals:    int(decimals),
		TotalSupply: decimal.NewFromBigInt(totalSupply, 0),
		ChainId:     w.chainId.Uint64(),
	}

	err = db.CreateTokenWithCache(tokenModel)
	if err != nil {
		log.Logger.Error("create token error", zap.Error(err), zap.String("tokenAddress", tokenAddress), zap.Any("total supply", totalSupply))
	}

	return tokenModel, nil
}
