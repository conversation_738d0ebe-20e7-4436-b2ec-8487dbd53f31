package fourmeme

import (
	"context"
	"encoding/json"
	"math/big"
	"testing"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/forta-network/go-multicall"
	muticalltype "gitlab.jcwork.net/cedefi/cedefi-evm-indexer/contracts/muticall"

	fourmeme "gitlab.jcwork.net/cedefi/cedefi-evm-indexer/contracts/fourmeme/tokenmanagerv2"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/contracts/uniswapv2"
)

const (
	bscRpcUrl = "https://api.chainup.net/bsc/mainnet/6e3198189f654b7fa468606e8dc791a4/"
	bscWssUrl = "wss://api.chainup.net/ws/bsc/mainnet/6e3198189f654b7fa468606e8dc791a4/"
)

func TestWatch(t *testing.T) {
	cli, err := ethclient.Dial(bscWssUrl)
	if err != nil {
		t.Fatal(err)
	}

	ctx := context.Background()
	ch := make(chan types.Log)
	sub, err := cli.SubscribeFilterLogs(ctx, ethereum.FilterQuery{
		Topics: [][]common.Hash{
			{common.HexToHash("0x0a5575b3648bae2210cee56bf33254cc1ddfbc7bf637c0af2ac18b14fb1bae19")},
		},
	}, ch)
	if err != nil {
		t.Fatal(err)
	}
	defer sub.Unsubscribe()

	pair, err := fourmeme.NewTokenManagerV2(common.Address{}, nil)
	if err != nil {
		t.Fatal(err)
	}

	for {
		select {
		case err := <-sub.Err():
			t.Fatal(err)
		case vLog := <-ch:
			swapLog, err := pair.ParseTokenSale(vLog)
			if err != nil {
				t.Fatal(err)
			}
			bytes, _ := json.Marshal(swapLog)
			t.Log(string(bytes))
			t1, t2, err := getPairAddr(swapLog.Raw.Address.String(), bscRpcUrl)
			if err != nil {
				t.Fatal(err)
			}
			t.Logf("token1:%s, token2:%s\n", t1, t2)
		}
	}

}

func getPairAddr(pairAddress string, rpcUrl string) (string, string, error) {
	caller, err := multicall.Dial(context.Background(), rpcUrl)
	if err != nil {
		//log.Logger.Error("dial rpc error", zap.Error(err))
		return "", "", err
	}

	pairContract, err := multicall.NewContract(uniswapv2.PairMetaData.ABI, pairAddress)
	if err != nil {
		//log.Logger.Error("new erc20 contract error", zap.Error(err))
		return "", "", err
	}

	calls, err := caller.Call(nil,
		pairContract.NewCall(new(muticalltype.AddressOutput), "token0"),
		pairContract.NewCall(new(muticalltype.AddressOutput), "token1"),
	)

	quoteAddr := calls[0].Outputs.(*muticalltype.AddressOutput).Value
	baseAddr := calls[1].Outputs.(*muticalltype.AddressOutput).Value

	return quoteAddr.String(), baseAddr.String(), nil
}

func TestTx(t *testing.T) {
	tx := "0x1ceb8bc3d0823a580d263b128b966dd9ff856483e1e00424b3b92298fa16efac"

	// 连接到 BSC RPC
	cli, err := ethclient.Dial(bscRpcUrl)
	if err != nil {
		t.Fatal(err)
	}

	// 获取交易 receipt
	txHash := common.HexToHash(tx)
	receipt, err := cli.TransactionReceipt(context.Background(), txHash)
	if err != nil {
		t.Fatal(err)
	}

	// 创建 fourmeme 合约解析器
	parser, err := fourmeme.NewTokenManagerV2(common.Address{}, nil)
	if err != nil {
		t.Fatal(err)
	}

	// TokenSale 事件签名
	tokenSaleEventSig := common.HexToHash("0x0a5575b3648bae2210cee56bf33254cc1ddfbc7bf637c0af2ac18b14fb1bae19")

	// 遍历所有日志，查找 TokenSale 事件
	for i, vLog := range receipt.Logs {
		t.Logf("Log %d: Address=%s, Topics=%v", i, vLog.Address.Hex(), vLog.Topics)

		// 检查是否是 TokenSale 事件
		if len(vLog.Topics) > 0 && vLog.Topics[0] == tokenSaleEventSig {
			t.Logf("Found TokenSale event at log index %d", i)

			// 解析 TokenSale 事件
			saleEvent, err := parser.ParseTokenSale(*vLog)
			if err != nil {
				t.Logf("Failed to parse TokenSale event: %v", err)
				continue
			}

			// 输出解析结果
			t.Logf("=== TokenSale Event Details ===")
			t.Logf("Token: %s", saleEvent.Token.Hex())
			t.Logf("Account: %s", saleEvent.Account.Hex())
			t.Logf("Price: %s", saleEvent.Price.String())
			t.Logf("Amount: %s", saleEvent.Amount.String())
			t.Logf("Cost: %s", saleEvent.Cost.String())
			t.Logf("Fee: %s", saleEvent.Fee.String())
			t.Logf("Offers: %s", saleEvent.Offers.String())
			t.Logf("Funds: %s", saleEvent.Funds.String())

			// 输出 JSON 格式
			eventBytes, _ := json.Marshal(saleEvent)
			t.Logf("JSON: %s", string(eventBytes))

			// 计算实际卖出数量（扣除手续费）
			actualAmount := new(big.Int).Sub(saleEvent.Amount, saleEvent.Fee)
			t.Logf("Actual sell amount (after fee): %s", actualAmount.String())
		}
	}
}
