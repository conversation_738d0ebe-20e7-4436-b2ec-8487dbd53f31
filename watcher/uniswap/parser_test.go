package uniswap

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"math/big"
	"testing"
	"time"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/shopspring/decimal"
	"github.com/test-go/testify/require"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/config"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/contracts/uniswapv2"
)

var (
	priv       = "9f5bb0d48b212400be5b2d464d47acf262f45aa81c71bf750d747841b9b019ae"
	wju        = common.HexToAddress("******************************************")
	usdt       = common.HexToAddress("******************************************")
	routerAddr = common.HexToAddress("******************************************")
	pairAddr   = common.HexToAddress("******************************************")
	cli        *ethclient.Client
	ctx        = context.Background()
)

func init() {
	ethCli, err := ethclient.Dial("https://rpc.juchain.org")
	if err != nil {
		panic(err)
	}
	cli = ethCli

}

func TestSwap(t *testing.T) {
	chainID, err := cli.ChainID(context.Background())
	if err != nil {
		t.Fatal(err)
	}

	senderPriKey, _ := crypto.HexToECDSA(priv)
	senderAddress := crypto.PubkeyToAddress(senderPriKey.PublicKey)
	t.Log("sender: ", senderAddress.String())

	auth, err := bind.NewKeyedTransactorWithChainID(senderPriKey, chainID)
	if err != nil {
		t.Fatal(err)
	}

	amount := big.NewInt(100000000000000000)
	path := []common.Address{wju, usdt}

	deadline := big.NewInt(time.Now().Unix() + 3600)

	router, err := uniswapv2.NewRouter(routerAddr, cli)
	if err != nil {
		t.Fatal(err)
	}

	tx, err := router.SwapExactETHForTokens(
		&bind.TransactOpts{From: senderAddress, Signer: auth.Signer, Value: amount},
		big.NewInt(0),
		path,
		senderAddress,
		deadline,
	)

	if err != nil {
		t.Fatal(err)
	}

	println(fmt.Sprintf("Buy %s tokens , hash : %s", amount.String(), tx.Hash().String()))
	return
}

func TestParseSwap(t *testing.T) {
	query := ethereum.FilterQuery{
		FromBlock: big.NewInt(1808821),
		ToBlock:   big.NewInt(1808821),
		//Addresses: []watcher_group.Address{pairAddr},
		Topics: [][]common.Hash{
			{common.HexToHash("0xd78ad95fa46c994b6551d0da85fc275fe613ce37657fb8d5e3d130840159d822")},
			//{watcher_group.HexToHash("0x0000000000000000000000006a647e09193a130b0cccbf26a1cf442491bdecc0")},
			//{watcher_group.HexToHash("0x000000000000000000000000399cc49fac069e5663bea331e9609f5bff2a8e90")},
		},
	}

	logs, err := cli.FilterLogs(context.Background(), query)
	if err != nil {
		t.Fatal(err)
	}

	pair, err := uniswapv2.NewPair(common.HexToAddress(""), nil)
	if err != nil {
		t.Fatal(err)
	}

	for _, l := range logs {
		swapLog, err := pair.ParseSwap(l)
		if err != nil {
			t.Fatal(err)
		}

		bytes, err := json.Marshal(swapLog)
		if err != nil {
			t.Fatal(err)
		}
		t.Log(string(bytes))

	}

}

func TestWs(t *testing.T) {
	ctx := context.Background()
	ch := make(chan types.Log)
	sub, err := cli.SubscribeFilterLogs(ctx, ethereum.FilterQuery{
		Topics: [][]common.Hash{
			{common.HexToHash("0xd78ad95fa46c994b6551d0da85fc275fe613ce37657fb8d5e3d130840159d822")},
		},
	}, ch)
	if err != nil {
		t.Fatal(err)
	}
	defer sub.Unsubscribe()

	pair, err := uniswapv2.NewPair(common.Address{}, nil)
	if err != nil {
		t.Fatal(err)
	}

	for {
		select {
		case err := <-sub.Err():
			t.Fatal(err)
		case vLog := <-ch:
			swapLog, err := pair.ParseSwap(vLog)
			if err != nil {
				t.Fatal(err)
			}

			bytes, _ := json.Marshal(swapLog)
			t.Log(string(bytes))
		}
	}

}

func TestGetFactory(t *testing.T) {
	pair, err := uniswapv2.NewPair(common.HexToAddress("******************************************"), cli)
	if err != nil {
		t.Fatal(err)
	}

	factory, err := pair.Factory(nil)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(factory.String())
}

func TestGetRealQuoteInfo(t *testing.T) {
	cases := []struct {
		name     string
		base     string
		quote    string
		expected string
	}{
		{
			name:     "baseIsRealQuote",
			base:     "0xc8e19C19479a866142B42fB390F2ea1Ff082E0D2",
			quote:    "0xaC86c6739212c6eDC9Fad3EE7C87a0fc4864f442",
			expected: "0xc8e19C19479a866142B42fB390F2ea1Ff082E0D2",
		},
		{
			name:     "quoteIsRealQuote",
			base:     "0xaC86c6739212c6eDC9Fad3EE7C87a0fc4864f442",
			quote:    "0xc8e19C19479a866142B42fB390F2ea1Ff082E0D2",
			expected: "0xc8e19C19479a866142B42fB390F2ea1Ff082E0D2",
		},
	}
	confFlag := flag.String("conf", "../../config/config-dev.yaml", "configuration file path")
	flag.Parse()
	cfg := config.LoadConfig("local", *confFlag)

	ps := NewParser(cfg.Chain[0])
	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			rq := ps.getRealQuoteInfo(c.base, c.quote)
			require.Equal(t, c.expected, rq.Address)
		})
	}
}

func TestGetQuotePrice(t *testing.T) {
	cases := []struct {
		name     string
		base     string
		quote    string
		expected decimal.Decimal
	}{
		{
			name:     "baseIsRealQuote",
			base:     "0xc8e19C19479a866142B42fB390F2ea1Ff082E0D2",
			quote:    "0xaC86c6739212c6eDC9Fad3EE7C87a0fc4864f442",
			expected: decimal.NewFromInt(1),
		},
		{
			name:     "quoteIsRealQuote",
			base:     "0xaC86c6739212c6eDC9Fad3EE7C87a0fc4864f442",
			quote:    "0xc8e19C19479a866142B42fB390F2ea1Ff082E0D2",
			expected: decimal.NewFromInt(1),
		},
	}

	confFlag := flag.String("conf", "../../config/config-dev.yaml", "configuration file path")
	flag.Parse()
	cfg := config.LoadConfig("local", *confFlag)

	ps := NewParser(cfg.Chain[0])
	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			p := ps.getRealQuotePrice(c.base, c.quote, decimal.NewFromInt(6))
			require.Equal(t, c.expected, p)
		})
	}
}
