package consts

import (
	"errors"
	"github.com/ethereum/go-ethereum/common"
	"math/big"
	"time"
)

const (
	EnvDev   = "dev"
	EnvTest  = "test"
	EnvProd  = "prd"
	EnvLocal = "local"

	TopicTradeEvent = "cedefi_event_trade"

	FourMemeTokenDecimals = 18
)

// redis key relate
const (
	TokenRedisKeyTpl              = "evm%d:token:%s"
	TokenExpireTime               = time.Hour * 24
	PoolRedisKeyTpl               = "evm%d:pool:%s"
	PoolBaseRedisKey              = "base"
	PoolStatQuoteAmountRedisKey   = "stat_now_quote_liq"
	PoolStatBaseAmountRedisKey    = "stat_now_base_liq"
	PoolStatPriceInNativeRedisKey = "price_in_native"
	PoolStatProgressRedisKey      = "progress"
	PoolStatMcInNativeRedisKey    = "mc_in_native"
	PoolExpireTime                = time.Hour * 24
	PoolExpireZsetTpl             = "evm%d_pool_expire_zset"
)

// chain relate
const (
	ChainIdETH     = uint64(1)
	ChainIdBSC     = uint64(56)
	ChainIdBase    = uint64(8453)
	ChainIdJuChain = uint64(210000)

	DexNameUnknown            = DexName("Unknown")
	DexNameUniswapV2          = DexName("Uniswap V2")
	DexNameJamm               = DexName("Jamm")
	DexNameJuSwap             = DexName("JuSwap")
	DexNameSushiSwap          = DexName("SushiSwap")
	DexNameCroDefiSwap        = DexName("CroDefiSwap")
	DexNameShibaSwap          = DexName("ShibaSwap")
	DexNameSaitaSwap          = DexName("SaitaSwap")
	DexNamePancakeSwap        = DexName("PancakeSwap")
	DexNameFourMeme           = DexName("FourMeme")
	DexNameRingV2             = DexName("RingV2")
	DexNameFraxswapV2         = DexName("Fraxswap V2")
	DexNameLinkSwap           = DexName("LinkSwap")
	DexNameOrionPoolV2        = DexName("OrionPoolV2")
	DexNameUniswapUniversalV2 = DexName("Uniswap Universal V2")
	DexNameUniswapV3          = DexName("Uniswap V3")
	DexNameUniswapV4          = DexName("Uniswap V4")
)

// topics
const (
	FourMemeTokenPurchaseTopic = "0x7db52723a3b2cdd6164364b3b766e65e540d7be48ffa89582956d8eaebe62942"
	FourMemeTokenSellTopic     = "0x0a5575b3648bae2210cee56bf33254cc1ddfbc7bf637c0af2ac18b14fb1bae19"
	UniswapV2SwapTopic         = "0xd78ad95fa46c994b6551d0da85fc275fe613ce37657fb8d5e3d130840159d822"
	UniswapV2SyncTopic         = "0x1c411e9a96e071241c2f21f7726b17ae89e3cab4c78be50e062b03a9fffbbad1"
)

var (
	ErrTokenNotFound = errors.New("token not included")
	ErrPoolNotFound  = errors.New("pool not included")
)

type DexMap map[string]DexName
type ChainDexMap map[uint64]DexMap

type DexName string

var (
	ErrInvalidSwapLog = errors.New("invalid swap log")

	ETHDex     = DexMap{}
	ETHFactory = DexMap{
		"******************************************": DexNameUniswapV2,
		"******************************************": DexNameSushiSwap,
		"******************************************": DexNameCroDefiSwap,
		"******************************************": DexNameShibaSwap,
		"******************************************": DexNameSaitaSwap,
		"******************************************": DexNamePancakeSwap,
		"******************************************": DexNameRingV2,
		"******************************************": DexNameFraxswapV2,
		"******************************************": DexNameLinkSwap,
		"******************************************": DexNameOrionPoolV2,
	}

	BSCDex     = DexMap{}
	BSCFactory = DexMap{}

	BaseDex = DexMap{
		"******************************************": DexNameUniswapUniversalV2,
		"******************************************": DexNameUniswapV2,
		"******************************************": DexNameUniswapV4,
	}
	BaseFactory = DexMap{}

	JuDex = DexMap{
		"******************************************": DexNameJamm,
	}
	JuFactory = DexMap{
		"******************************************": DexNameJamm,
		"******************************************": DexNameJuSwap,
	}

	JuUsdt = common.HexToAddress("******************************************")

	ChainDex = ChainDexMap{
		1:      ETHDex,
		56:     BSCDex,
		8453:   BaseDex,
		210000: JuDex,
	}

	ChainFactory = ChainDexMap{
		1:      ETHFactory,
		56:     BSCFactory,
		8453:   BaseFactory,
		210000: JuFactory,
	}

	FourMemeTokenSupply, _ = new(big.Int).SetString("1000000000000000000000000000", 10)
)

var (
	ChainIDList = []uint64{
		ChainIdETH,     // ETH
		ChainIdBSC,     // BSC
		ChainIdBase,    // Base
		ChainIdJuChain, // Ju
	}
)
