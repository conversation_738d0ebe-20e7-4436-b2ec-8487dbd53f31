package models

import (
	"github.com/shopspring/decimal"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/types"
)

// Activity is a struct that represents a trade in the database
type Activity struct {
	ID               int64              `json:"id" gorm:"primary_key;auto_increment"`
	TokenAddress     string             `json:"token_address" gorm:"type:varchar(128);index:idx_token"`              //token地址 与base或quote相同，因为池子中base和quote可能是相反的
	PairAddress      string             `json:"pair_address" gorm:"type:varchar(128);index:idx_pair"`                //池子地址
	BaseAddress      string             `json:"base_address" gorm:"type:varchar(128);index:idx_base"`                //base token地址
	BaseDecimals     int                `json:"base_decimals" gorm:"type:int;"`                                      //base token精度
	QuoteAddress     string             `json:"quote_address" gorm:"type:varchar(128);index:idx_quote"`              //quote token地址
	QuoteDecimals    int                `json:"quote_decimals" gorm:"type:int;"`                                     //quote token精度
	Act              types.ActivityType `json:"act" gorm:"type:int;not null"`                                        //交易类型
	Maker            string             `json:"maker" gorm:"type:varchar(128);not null; index:idx_maker"`            //交易人地址
	BaseAmount       decimal.Decimal    `json:"base_amount" gorm:"type:decimal(65,0);not null"`                      //交易base token数量，不带精度
	QuoteAmount      decimal.Decimal    `json:"quote_amount" gorm:"type:decimal(65,0);not null"`                     //交易quote token数量，不带精度
	PriceInNative    decimal.Decimal    `json:"price_in_native" gorm:"type:decimal(64,30);not null"`                 //base token基于native本位的价格 --> base token基于realQuote本位的价格（xyu edited）
	PriceInUsd       decimal.Decimal    `json:"price_in_usd" gorm:"type:decimal(64,30);not null"`                    //base token基于usd本位的价格
	NativePriceInUsd decimal.Decimal    `json:"native_price_in_usd" gorm:"type:decimal(64,30);not null"`             //native 价格 (平台币的价格：JU/SOL的价格)
	VolumeInUsd      decimal.Decimal    `json:"volume_in_usd" gorm:"type:decimal(64,30);not null"`                   //基于USD本位的交易额
	Height           int64              `json:"height" gorm:"not null"`                                              //交易块高
	Tx               string             `json:"tx" gorm:"type:varchar(128);not null;index:,unique,composite:tx_idx"` //交易hash
	Idx              int64              `json:"idx" gorm:"not null;index:,unique,composite:tx_idx"`                  //交易序号
	Ts               int64              `json:"ts" gorm:"not null"`                                                  //交易时间戳
	DexName          string             `json:"dex_name" gorm:"type:varchar(64);not null"`                           //dex名称
	ChainId          uint64             `json:"chain_id" gorm:"not null"`                                            //ChainId
	CreatedAt        int64              `json:"created_at" gorm:"autoCreateTime"`
}
