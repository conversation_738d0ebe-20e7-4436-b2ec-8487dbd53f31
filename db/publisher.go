package db

import (
	"encoding/json"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/common"
	"time"

	"github.com/ThreeDotsLabs/watermill"
	"github.com/ThreeDotsLabs/watermill-kafka/v3/pkg/kafka"
	"github.com/ThreeDotsLabs/watermill/message"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/config"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/consts"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/db/models"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/log"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/types"
	"go.uber.org/zap"
)

var pub message.Publisher

func InitPublisher(cfg config.KafkaConfig) {
	if common.IsEnvLocal() {
		return
	}

	var err error
	pub, err = kafka.NewPublisher(
		kafka.PublisherConfig{Brokers: cfg.Brokers, Marshaler: kafka.DefaultMarshaler{}},
		watermill.NewStdLogger(false, false),
	)

	if err != nil {
		log.Logger.Error("failed to create Kafka publisher", zap.Error(err))
		time.Sleep(5 * time.Second)
		InitPublisher(cfg)
	}

	log.Logger.Info("kafka init success")
}

func PublishActivity(activity models.Activity) error {
	msg := ActivityMessage{
		ID:               activity.ID,
		TokenAddress:     activity.TokenAddress,
		PairAddress:      activity.PairAddress,
		BaseAddress:      activity.BaseAddress,
		BaseDecimals:     activity.BaseDecimals,
		QuoteAddress:     activity.QuoteAddress,
		QuoteDecimals:    activity.QuoteDecimals,
		Act:              activity.Act,
		Maker:            activity.Maker,
		BaseAmount:       activity.BaseAmount.String(),
		QuoteAmount:      activity.QuoteAmount.String(),
		PriceInNative:    activity.PriceInNative.String(),
		PriceInUsd:       activity.PriceInUsd.String(),
		NativePriceInUsd: activity.NativePriceInUsd.String(),
		VolumeInUsd:      activity.VolumeInUsd.String(),
		Height:           activity.Height,
		Tx:               activity.Tx,
		Idx:              activity.Idx,
		Ts:               activity.Ts,
		DexName:          activity.DexName,
		ChainId:          activity.ChainId,
		CreatedAt:        activity.CreatedAt,
	}

	log.Logger.Info("publish activity", zap.String("hash", activity.Tx), zap.Any("msg", msg))
	err := publishTopic(consts.TopicTradeEvent, msg)
	if err != nil {
		log.Logger.Error("publish activity error", zap.String("hash", activity.Tx), zap.Error(err))
	}

	return err
}

func publishTopic(topic string, msg interface{}) error {
	if common.IsEnvLocal() {
		return nil
	}
	bytes, _ := json.Marshal(msg)
	return pub.Publish(topic, message.NewMessage(watermill.NewUUID(), bytes))
}

type ActivityMessage struct {
	ID               int64              `json:"id"`
	TokenAddress     string             `json:"token_address"`
	PairAddress      string             `json:"pair_address"`
	BaseAddress      string             `json:"base_address"`
	BaseDecimals     int                `json:"base_decimals"`
	QuoteAddress     string             `json:"quote_address"`
	QuoteDecimals    int                `json:"quote_decimals"`
	Act              types.ActivityType `json:"act"`
	Maker            string             `json:"maker"`
	BaseAmount       string             `json:"base_amount"`
	QuoteAmount      string             `json:"quote_amount"`
	PriceInNative    string             `json:"price_in_native"`
	PriceInUsd       string             `json:"price_in_usd"`
	NativePriceInUsd string             `json:"native_price_in_usd"`
	VolumeInUsd      string             `json:"volume_in_usd"`
	Height           int64              `json:"height"`
	Tx               string             `json:"tx"`
	Idx              int64              `json:"idx"`
	Ts               int64              `json:"ts"`
	DexName          string             `json:"dex_name"`
	ChainId          uint64             `json:"chain_id"`
	CreatedAt        int64              `json:"created_at"`
}
