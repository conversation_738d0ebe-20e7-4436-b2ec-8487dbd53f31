package main

import (
	"context"
	"flag"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/watcher/block"
	"os"

	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/config"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/consts"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/db"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/log"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/server/health"
	"gitlab.jcwork.net/cedefi/cedefi-evm-indexer/watcher/native_price"
	"go.uber.org/zap"
)

func main() {
	env := os.Getenv("ENV")
	if env == "" {
		env = consts.EnvLocal
		_ = os.Setenv("ENV", env)
	}
	log.InitLogger(env)

	log.Logger.Info("cedefi-evm-indexer started", zap.String("env", env))

	confFlag := flag.String("conf", "config.yaml", "configuration file path")
	flag.Parse()

	cfg := config.LoadConfig(env, *confFlag)

	db.InitDb(cfg.Db)
	db.InitRdb(cfg.Rdb)
	db.InitPublisher(cfg.Kafka)
	db.StartSwapStoreLoop(context.Background()) //start batch insert service

	native_price.InitPriceWatcherGroup(cfg.Chain)

	block.NewWatcher(cfg.Chain).Start()

	health.NewServer(cfg.Server.Health).Serve()

	select {}
}
