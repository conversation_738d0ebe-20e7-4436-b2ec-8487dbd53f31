server:
  health:
    port: 8031
  grpc:
    port: 9000

db:
  host: "cex-mysql-cedefi-test.cluster-c5mgk4qm8m2z.ap-southeast-1.rds.amazonaws.com"
  port: 3358
  user: "cedefi_rw"
  password: "Nf1GqxhbZ4p4bXSR"
  dbname: "cedefi_evm_indexer"

rdb:
  host: "clustercfg.cex-redis-cedefi-test.exwdne.apse1.cache.amazonaws.com"
  port: 6379
  password: "xmBIH6iQDgfbVeYg"
  db: 0
  insecure_skip_verify: true

kafka:
  brokers:
    - "b-1.cexkafkacedefites.465odg.c5.kafka.ap-southeast-1.amazonaws.com:9092"
    - "b-2.cexkafkacedefites.465odg.c5.kafka.ap-southeast-1.amazonaws.com:9092"
    - "b-3.cexkafkacedefites.465odg.c5.kafka.ap-southeast-1.amazonaws.com:9092"
  user: ""
  password: ""

chain:
  - # Ju
    chain_id: 210000
    rpc_url: "https://rpc.juchain.org"
    ws_url: "wss://ws.juchain.org"
    forward_enabled: true
    backward_enabled: false
    max_block_step: 100
    multi_call_addr: "0x71306Fc2d0260DBe0eA4ff07888FA9e8d7fc22d0"
    # 主币包装合约
    w_native: "0x4d1B49B424afd7075d3c063adDf97D5575E1c7E2"
    w_native_decimals: 18
    # 主币与稳定币的池子
    w_native_pair: "0x4cF7fb4674254d2852FeA7FA79438456A9F271c9"
    # U 的精度
    u_decimals: 18
    uniswapv2_enabled: true
    supported_quote_tokens: # 支持的计价代币;配置之后，相应的货币对将被解析；否则，只解析native token货币对；
      -
        address: "0xc8e19C19479a866142B42fB390F2ea1Ff082E0D2" # USDT contract address
        decimals: 18
  - # BSC
    chain_id: 56
    rpc_url: "https://api.chainup.net/bsc/mainnet/6e3198189f654b7fa468606e8dc791a4/"
    ws_url: "wss://api.chainup.net/ws/bsc/mainnet/6e3198189f654b7fa468606e8dc791a4/"
    forward_enabled: true
    backward_enabled: false
    max_block_step: 100
    # 主币包装合约
    w_native: "******************************************"
    w_native_decimals: 18
    # 主币与稳定币的池子
    w_native_pair: "******************************************"
    # U 的精度
    u_decimals: 18
    uniswapv2_enabled: true
    fourmeme_enabled: false
#  - # ETH
#    chain_id: 1
#    rpc_url: "https://api.chainup.net/ethereum/mainnet/6e3198189f654b7fa468606e8dc791a4/"
#    ws_url: "wss://api.chainup.net/ws/ethereum/mainnet/6e3198189f654b7fa468606e8dc791a4/"
#    # 向前爬取开关
#    forward_enabled: false
#    # 向后爬取开关
#    backward_enabled: false
#    # 爬取最大步长
#    max_block_step: 100
#    # 主币包装合约
#    w_native: "******************************************"
#    w_native_decimals: 18
#    # 主币与稳定币的池子
#    w_native_pair: "******************************************"
#    # U 的精度
#    u_decimals: 6
#    # uniswapv2 协议解析开关
#    uniswapv2_enabled: true
#  - # Base
#    chain_id: 8453
#    rpc_url: "https://api.chainup.net/base/mainnet/6e3198189f654b7fa468606e8dc791a4/"
#    ws_url: "wss://api.chainup.net/ws/base/mainnet/6e3198189f654b7fa468606e8dc791a4/"
#    forward_enabled: false
#    backward_enabled: false
#    max_block_step: 100
#    # 主币包装合约
#    w_native: "******************************************"
#    w_native_decimals: 18
#    # 主币与稳定币的池子
#    w_native_pair: "0x88A43bbDF9D098eEC7bCEda4e2494615dfD9bB9C"
#    # U 的精度
#    u_decimals: 6
#    uniswapv2_enabled: false
